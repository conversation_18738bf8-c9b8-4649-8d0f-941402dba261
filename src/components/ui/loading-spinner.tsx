"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/libs/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  text?: string;
}

export function LoadingSpinner({ size = "md", className, text }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  const spinTransition = {
    repeat: Infinity,
    ease: "linear",
    duration: 1,
  };

  return (
    <div className={cn("flex flex-col items-center justify-center", className)}>
      <motion.div
        animate={{ rotate: 360 }}
        transition={spinTransition}
        className={cn("border-primary rounded-full border-t-2", sizeClasses[size])}
      />
      {text && <p className="text-muted-foreground mt-2 text-sm">{text}</p>}
    </div>
  );
}
