"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { LoadingSpinner } from "./loading-spinner";
import { cn } from "@/libs/utils";

interface LoadingOverlayProps {
  isLoading: boolean;
  text?: string;
  fullScreen?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export function LoadingOverlay({
  isLoading,
  text = "Loading...",
  fullScreen = false,
  className,
  children,
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}

      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "bg-background/80 z-50 flex flex-col items-center justify-center backdrop-blur-sm",
              fullScreen ? "fixed inset-0" : "absolute inset-0"
            )}
          >
            <div className="bg-card/50 flex flex-col items-center rounded-xl p-6 shadow-lg">
              <LoadingSpinner size="lg" />
              {text && (
                <motion.p
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-foreground mt-4 font-medium"
                >
                  {text}
                </motion.p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
