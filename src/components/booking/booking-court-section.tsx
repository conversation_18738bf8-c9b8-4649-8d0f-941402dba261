import React, { useState, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { ChevronDown, Plus, Minus, InfoIcon } from "lucide-react";
import { useGetPublicCourtById, useGetPublicCourtList } from "@/api/booking-service";

// Types for the booking form
interface BookingFormData {
  court: string;
  subCourt: string;
  date: string;
  time: string;
  duration: string;
  players: number;
  //   clutchAi: boolean;
  //   package: string;
}

const BookingCourtSection = () => {
  const locationId = 29;

  // State management for booking form
  const [bookingData, setBookingData] = useState<BookingFormData>({
    court: "",
    subCourt: "",
    date: "",
    time: "",
    duration: "",
    players: 2,
    // clutchAi: true, // Default to On
    // package: "Allay Package",
  });

  const { publicCourtList, publicCourtListLoading, publicCourtListError } = useGetPublicCourtList({
    page: 1,
    limit: 10,
    courtLocationId: locationId,
  });

  //   console.log("publicCourtList", publicCourtList);

  const { publicCourtDetails, publicCourtDetailsLoading, publicCourtDetailsError } =
    useGetPublicCourtById({
      courtId: publicCourtList[0]?.id || 0,
    });

  console.log("publicCourtDetails", publicCourtDetails);

  // Optimized handler for updating booking data
  const updateBookingData = useCallback(
    <K extends keyof BookingFormData>(field: K, value: BookingFormData[K]) => {
      setBookingData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  // Memoized clutch AI status text
  //   const clutchAiStatus = useMemo(() => {
  //     return bookingData.clutchAi ? "On" : "Off";
  //   }, [bookingData.clutchAi]);

  // Handler for clutch AI toggle
  //   const handleClutchAiChange = useCallback(
  //     (enabled: boolean) => {
  //       updateBookingData("clutchAi", enabled);
  //     },
  //     [updateBookingData]
  //   );

  // Memoized pricing calculations
  const pricing = useMemo(() => {
    const courtBooking = 40.0;
    // const clutchAiCost = bookingData.clutchAi ? 10.0 : 0.0;
    const allayPackage = 0.0;
    const subtotal = courtBooking + allayPackage;
    const vat = subtotal * 0.2; // 20% VAT
    const total = subtotal + vat;

    return {
      courtBooking,
      allayPackage,
      subtotal,
      vat,
      total,
    };
  }, []);

  return (
    <div className="container mx-auto px-4 py-1">
      <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
        <div className="flex w-full flex-col items-start justify-start gap-10 rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:justify-between lg:p-10">
          {/* Left Section - Form Controls */}
          <div className="flex w-full flex-col items-start justify-start gap-3 lg:w-1/2">
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Court
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.court || "Select court"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["Court 1", "Court 2", "Court 3", "Court 4"].map((court) => (
                  <DropdownMenuItem
                    key={court}
                    onClick={() => updateBookingData("court", court)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {court}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Date
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.date || "Select date"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["Today", "Tomorrow", "Wed 25 April", "Thu 26 April", "Fri 27 April"].map(
                  (date) => (
                    <DropdownMenuItem
                      key={date}
                      onClick={() => updateBookingData("date", date)}
                      className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                    >
                      <span className="font-helvetica text-[15px] font-normal text-black">
                        {date}
                      </span>
                    </DropdownMenuItem>
                  )
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Time
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.time || "Select time"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {[
                  "9:00 AM",
                  "10:00 AM",
                  "11:00 AM",
                  "12:00 PM",
                  "1:00 PM",
                  "2:00 PM",
                  "3:00 PM",
                  "4:00 PM",
                  "5:00 PM",
                ].map((time) => (
                  <DropdownMenuItem
                    key={time}
                    onClick={() => updateBookingData("time", time)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {time}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Duration
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.duration || "Select duration"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["30 minutes", "60 minutes", "90 minutes", "120 minutes"].map((duration) => (
                  <DropdownMenuItem
                    key={duration}
                    onClick={() => updateBookingData("duration", duration)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {duration}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* Divider - Hidden on mobile, visible on desktop */}
          <div className="hidden lg:block" data-svg-wrapper>
            <svg
              width="2"
              height="640"
              viewBox="0 0 2 640"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <line x1="1" y1="0" x2="1" y2="640" stroke="#EBEBEB" strokeWidth="2" />
            </svg>
          </div>

          {/* Right Section - Player Controls and Booking Summary */}
          <div className="flex w-full flex-col items-start justify-between lg:w-1/2">
            <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
              <div className="flex w-full items-center justify-between">
                <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                  Number of Players
                </div>
                <div className="flex items-center justify-center gap-4">
                  <button
                    onClick={() =>
                      updateBookingData("players", Math.max(1, bookingData.players - 1))
                    }
                    className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                  >
                    <Minus className="h-4 w-4 text-[#D8D8D8]" />
                  </button>
                  <div className="font-helvetica min-w-[60px] text-center text-[15px] leading-[35px] font-normal text-black">
                    {bookingData.players}
                  </div>
                  <button
                    onClick={() => updateBookingData("players", bookingData.players + 1)}
                    className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                  >
                    <Plus className="h-4 w-4 text-black" />
                  </button>
                </div>
              </div>

              {/* <div className="flex w-full flex-col items-start justify-start gap-2">
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                    Clutch Ai
                  </div>
                  <div className="flex items-center gap-3">
                    <InfoIcon />
                    <Switch
                      checked={bookingData.clutchAi}
                      onCheckedChange={(checked) => handleClutchAiChange(checked)}
                      className="data-[state=checked]:bg-[#ddba0a] data-[state=unchecked]:bg-gray-300"
                    />
                  </div>
                </div>
                <div className="font-helvetica w-full text-[13px] leading-snug font-normal text-black">
                  The Clutch Cam automates content creation from your courts — generating
                  Instagrammable highlight reels & performance stats for every game.
                </div>
              </div> */}

              {/* <DropdownMenu>
                <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] bg-[#fffaed] px-[25px] py-2 outline-1 outline-[#ddba0a] transition-colors outline-dashed hover:bg-[#fff8e1]">
                  <span className="font-helvetica text-[15px] leading-[35px] font-normal text-[#1c5534]">
                    {bookingData.package || "Allay Package"}
                  </span>
                  <ChevronDown className="h-4 w-12 stroke-2 text-[#ddba0a]" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border-dashed border-[#ddba0a] bg-[#fffaed] shadow-lg">
                  {["Allay Package", "Premium Package", "Basic Package", "Elite Package"].map(
                    (packageOption) => (
                      <DropdownMenuItem
                        key={packageOption}
                        onClick={() => updateBookingData("package", packageOption)}
                        className="cursor-pointer px-[25px] py-3 text-[#1c5534] hover:bg-[#fff8e1]"
                      >
                        <span className="font-helvetica text-[15px] font-normal text-[#1c5534]">
                          {packageOption}
                        </span>
                      </DropdownMenuItem>
                    )
                  )}
                </DropdownMenuContent>
              </DropdownMenu> */}
            </div>
            <div className="flex w-full flex-col items-start justify-start gap-10 py-2 lg:mt-10">
              <div className="flex w-full flex-col items-start justify-between gap-4 rounded-[20px] border-[#ddba0a] bg-[#fffaed] p-4 shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)] sm:flex-row sm:items-end sm:p-6">
                <div className="flex-1">
                  <p className="font-helvetica text-base leading-snug font-medium text-black">
                    Booking Details
                  </p>
                  <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                    {bookingData.court || "Court 2"}
                  </p>
                  <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                    {bookingData.date || "Wed 25 April"}
                  </p>
                  <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                    {bookingData.time || "1:30PM"} - {bookingData.duration || "60 mins"}
                  </p>
                  {/* <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                    {bookingData.players} Players Clutch Ai - {clutchAiStatus}
                  </p> */}
                </div>
                <div className="flex w-full items-center justify-center gap-2.5 py-2.5 sm:w-auto">
                  <div className="font-helvetica text-center text-[22px] leading-normal font-bold text-[#1c5534]">
                    Pay ${pricing.total.toFixed(2)}
                  </div>
                </div>
              </div>
              <button className="flex w-full items-center justify-center rounded-full bg-[#ddba0a] px-10 py-4 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)] transition-colors hover:bg-[#c4a609]">
                <div className="font-helvetica text-primary text-center text-base leading-none font-bold">
                  Book Now
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
