"use client";
import BookingTypeSelector from "./booking-type-selector";
import SportSelector from "./sport-selector";

import { useGetPublicCourtLocations } from "@/api/booking-service";

interface BookingSectionProps {
  locationSlug: string;
}

const BookingSection = ({ locationSlug }: BookingSectionProps) => {
  // const { publicCourtLocationList } = useGetPublicCourtLocations({ page: 1, limit: 10 });

  // console.log("publicCourtLocationList", publicCourtLocationList);

  return (
    <div className="flex w-full flex-col items-center">
      <div className="w-full flex-col items-center justify-start">
        <h1 className="font-helvetica justify-center text-center text-2xl font-bold text-[#1c5534] md:text-4xl">
          Booking Options
        </h1>
        <p className="font-helvetica justify-center self-stretch text-center font-normal text-[#364153]">
          Choose how you play. Book the experience that fits you.
        </p>
      </div>

      <SportSelector />
      <BookingTypeSelector />
    </div>
  );
};

export default BookingSection;
