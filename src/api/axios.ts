import axios, { AxiosRequestConfig } from "axios";

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL!,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    if (typeof window === "undefined") {
      return config;
    }
    const token: any = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (res) => res,
  (error) => {
    console.error("Response Axios Error:", error);
    if (error.code === "ERR_NETWORK") {
      error.isNetworkError = true;
      window.location.href = "/";
    }
    return Promise.reject((error.response && error.response.data) || "Something went wrong");
  }
);

export const epicFetcher = async (
  args: string | [string, AxiosRequestConfig]
) => {
  const [url, config] = Array.isArray(args) ? args : [args];
  const res = await axiosInstance?.get(url, { ...config });
  return res.data;
};

export default axiosInstance;

export const endpoints = {
  public: {
    packages: "/public/packages",
    blog: {
      list: "/public/blog/list",
      content: "/public/blog/content",
    },
  },
  auth: {
    social: {
      google: "/public/auth/social/google",
      apple: "/public/auth/social/appleWeb",
    },
    updateUserEmailOrPhone: "/mob_app/auth/user/userEmailPhoneUpdate",
    updateUserVerifyEmailOrPhone: "/mob_app/auth/user/userVerifyEmailPhoneUpdate",
    removeProfileImage: "/mob_app/auth/user/removeProfileImage",
    login: "/public/auth/loginWithOtp",
    sendOtp: "/public/auth/sendOtp",
    autoLogin: "/mob_app/auth/autoLogin",
    purchaseSetup: "/public/subscribe/packagePurchaseSetup",
    updateUserProfile: "/mob_app/auth/user/updateUserProfile",
  },
  payment: {
    setupIntent: "/mob_app/auth/stripe/createSetupIntent",
    addPayment: "/mob_app/auth/stripe/addPaymentMethod",
    getList: "/mob_app/auth/stripe/getPaymentMethods",
    deletePayment: "/mob_app/auth/stripe/deletePaymentMethod",
    setDefault: "/mob_app/auth/stripe/setDefaultPaymentMethod",
  },
  booking: {
    getAllPublicCourtLocations: "/public/court_locations",
    getAllPublicCourtList: "/public/courts/getCourtList",
    getPublicCourtById: "/public/courts/getCourtbyId",
  }
};
